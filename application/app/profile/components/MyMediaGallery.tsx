/** @format */

import { colors } from '@/app/colors';
import {
	MediaDeleteButton,
	MediaUploadComponent,
} from '@/app/shared/media/components';
import { UploadResult } from '@/app/shared/media/types';
import { useSession } from 'next-auth/react';
import React, { useEffect, useState } from 'react';
import {
	FiCalendar,
	FiMapPin,
	FiRefreshCw,
	FiUpload,
	FiUser,
	FiX,
} from 'react-icons/fi';

const MyMediaGallery: React.FC = () => {
	const { data: session } = useSession();
	const [media, setMedia] = useState<
		{
			id: string;
			media_url: string;
			thumbnail_url?: string;
			media_type: 'photo' | 'video';
			caption?: string;
			created_at: string;
			like_count: number;
			favorite_count: number;
			poi_name?: string;
			poi_city?: string;
			poi_district?: string;
			poi_category?: string;
			poi_subcategory?: string;
			uploaded_by_username?: string;
			uploaded_by_name?: string;
			metadata?: Record<string, unknown>;
			is_verified?: boolean;
			poiId?: string;
			poi_id?: string;
		}[]
	>([]);
	const [showUpload, setShowUpload] = useState(false);
	const [loading, setLoading] = useState(false);

	useEffect(() => {
		if (session?.user?.id) {
			fetchMedia();
		}
	}, [session?.user?.id]);

	const fetchMedia = async () => {
		if (!session?.user?.id) return;
		setLoading(true);
		try {
			const res = await fetch(`/api/pois/media?userId=${session.user.id}`);
			const data = await res.json();
			if (data.success) {
				setMedia(data.media || []);
			}
		} catch (error) {
			console.error('Error fetching media:', error);
		} finally {
			setLoading(false);
		}
	};

	const handleUploadComplete = (results: UploadResult[]) => {
		console.log('Media upload completed:', results);
		setShowUpload(false);
		fetchMedia(); // Refresh the media gallery
	};

	const handleUploadError = (error: string) => {
		console.error('Media upload error:', error);
		alert('Failed to upload media: ' + error);
	};

	return (
		<div
			className='border backdrop-blur-sm shadow-lg overflow-hidden'
			style={{
				background: 'rgba(255, 255, 255, 0.9)',
				borderColor: colors.ui.gray200,
			}}>
			{/* Media Gallery Section - Square Edges */}
			<div style={{ backgroundColor: colors.neutral.cloudWhite }}>
				<div className='space-y-4 md:space-y-6 lg:space-y-8 p-4 md:p-6 lg:p-8'>
					{/* Header - Following Design Rules */}
					<div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 md:gap-6'>
						<div className='space-y-2'>
							<div className='flex items-center gap-3'>
								<div
									className='w-6 h-6 md:w-8 md:h-8 flex items-center justify-center rounded'
									style={{ backgroundColor: colors.brand.primary }}>
									<span className='text-white text-sm md:text-lg'>📸</span>
								</div>
								<h3
									className='text-lg md:text-xl lg:text-2xl font-semibold'
									style={{ color: colors.neutral.textBlack }}>
									My Photo Gallery
								</h3>
							</div>
							<p
								className='text-xs md:text-sm font-medium'
								style={{ color: colors.neutral.slateGray }}>
								{media.length} {media.length === 1 ? 'photo' : 'photos'} •
								Latest first
							</p>
						</div>

						<div className='flex items-center gap-4'>
							{/* Upload Button - Square Edges */}
							<button
								onClick={() => setShowUpload(true)}
								className='flex items-center gap-2 md:gap-3 px-4 py-2 md:px-6 md:py-3 text-white font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'
								style={{ backgroundColor: colors.brand.primary }}>
								<FiUpload className='h-4 w-4 md:h-5 md:w-5' />
								<span className='text-sm md:text-base'>Add Photos</span>
							</button>
						</div>
					</div>

					{/* Upload Modal - Square Edges */}
					{showUpload && (
						<div className='fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4'>
							<div
								className='border backdrop-blur-sm max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl'
								style={{
									background: 'rgba(255, 255, 255, 0.9)',
									borderColor: colors.ui.gray200,
								}}>
								<div className='p-4 md:p-6 lg:p-8'>
									<div className='flex items-center justify-between mb-4 md:mb-6 lg:mb-8'>
										<div className='flex items-center gap-3'>
											<div
												className='w-8 h-8 md:w-10 md:h-10 flex items-center justify-center shadow-lg'
												style={{ backgroundColor: colors.brand.blue }}>
												<FiUpload className='h-4 w-4 md:h-5 md:w-5 text-white' />
											</div>
											<div>
												<h3
													className='text-lg md:text-xl font-bold'
													style={{ color: colors.neutral.textBlack }}>
													Add Photos
												</h3>
												<p
													className='text-xs md:text-sm'
													style={{ color: colors.neutral.slateGray }}>
													Share your experiences
												</p>
											</div>
										</div>
										<button
											onClick={() => setShowUpload(false)}
											className='p-2 transition-all duration-200 border'
											style={{
												backgroundColor: colors.ui.gray100,
												borderColor: colors.ui.gray200,
												color: colors.neutral.slateGray,
											}}>
											<FiX className='h-5 w-5 md:h-6 md:w-6' />
										</button>
									</div>

									<MediaUploadComponent
										uploadType='poi_media'
										onUploadComplete={handleUploadComplete}
										onUploadError={handleUploadError}
										maxFiles={10}
										showPOISearch={true}
									/>
								</div>
							</div>
						</div>
					)}

					{/* Media Content */}
					{loading && media.length === 0 ? (
						<div className='flex flex-col items-center justify-center py-12 md:py-16 lg:py-20'>
							<div className='relative'>
								<FiRefreshCw
									className='h-8 w-8 md:h-12 md:w-12 animate-spin'
									style={{ color: colors.brand.blue }}
								/>
							</div>
							<p
								className='font-medium mt-4 text-sm md:text-base'
								style={{ color: colors.neutral.slateGray }}>
								Loading amazing photos...
							</p>
						</div>
					) : media.length === 0 ? (
						<div className='text-center py-12 md:py-16 lg:py-20'>
							<div className='relative inline-block mb-6 md:mb-8'>
								<div className='text-6xl md:text-8xl mb-4 relative z-10'>
									📷
								</div>
							</div>
							<div className='max-w-md mx-auto space-y-4'>
								<h3
									className='text-xl md:text-2xl font-bold'
									style={{ color: colors.neutral.textBlack }}>
									No photos yet
								</h3>
								<p
									className='leading-relaxed text-sm md:text-base'
									style={{ color: colors.neutral.slateGray }}>
									Start building your photo collection. Share your experiences
									with the community!
								</p>
								<button
									onClick={() => setShowUpload(true)}
									className='inline-flex items-center gap-2 px-4 py-2 md:px-6 md:py-3 text-white font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'
									style={{ backgroundColor: colors.brand.primary }}>
									<FiUpload className='w-4 h-4 md:w-5 md:h-5' />
									<span className='text-sm md:text-base'>Add First Photo</span>
								</button>
							</div>
						</div>
					) : (
						<>
							{/* Photo Grid - Square Edges, Responsive */}
							<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8'>
								{media.map((item, index) => (
									<div
										key={item.id || index}
										className='group relative border backdrop-blur-sm shadow-lg overflow-hidden hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1'
										style={{
											background: 'rgba(255, 255, 255, 0.9)',
											borderColor: colors.ui.gray200,
										}}>
										{/* Media Content */}
										<div className='relative aspect-square overflow-hidden'>
											{item.media_type === 'photo' ? (
												<img
													src={item.thumbnail_url || item.media_url}
													alt={item.caption || 'Photo'}
													className='w-full h-full object-cover group-hover:scale-105 transition-transform duration-200'
													loading='lazy'
												/>
											) : (
												<div className='relative'>
													<video
														src={item.media_url}
														className='w-full h-full object-cover'
														poster={item.thumbnail_url}
													/>
													<div
														className='absolute top-2 md:top-3 left-2 md:left-3 px-2 py-1 text-white text-xs font-medium'
														style={{ backgroundColor: 'rgba(0, 0, 0, 0.7)' }}>
														📹 Video
													</div>
												</div>
											)}

											{/* Hover Overlay */}
											<div className='absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300'></div>

											{/* Action Buttons */}
											<div className='absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 transition-all duration-300'>
												<div className='flex items-center justify-between'>
													<div className='flex items-center gap-2'>
														<FiMapPin className='w-3 h-3 text-white' />
														<span className='text-xs text-white font-medium'>
															{item.poi_name ||
																`POI ${item.poiId || item.poi_id || 'Unknown'}`}
														</span>
													</div>
													<MediaDeleteButton
														mediaId={item.id}
														size='small'
														onDeleteSuccess={() => {
															console.log('Media deleted successfully');
															fetchMedia(); // Refresh the gallery
														}}
														onDeleteError={(error) => {
															console.error('Delete error:', error);
															alert('Failed to delete media: ' + error);
														}}
													/>
												</div>
											</div>
										</div>

										{/* Caption */}
										{item.caption && (
											<div className='p-3'>
												<p className='text-sm text-gray-700 line-clamp-2 leading-relaxed'>
													{item.caption}
												</p>
											</div>
										)}

										{/* User Info */}
										<div className='px-3 pb-3'>
											<div className='flex items-center gap-2 text-xs text-gray-500'>
												<FiUser className='w-3 h-3' />
												<span>{session?.user?.name || 'You'}</span>
												<span>•</span>
												<FiCalendar className='w-3 h-3' />
												<span>
													{new Date(item.created_at).toLocaleDateString()}
												</span>
											</div>
										</div>
									</div>
								))}
							</div>
						</>
					)}
				</div>
			</div>
		</div>
	);
};

export default MyMediaGallery;
