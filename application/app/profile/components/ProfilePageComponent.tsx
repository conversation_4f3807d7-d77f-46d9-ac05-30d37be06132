/** @format */

import { colors } from '@/app/colors';
import { UserProfile } from '@/app/shared/profile';
import { UserInteractionsProvider } from '@/app/shared/userInteractions/shared/context/UserInteractionsProvider';
import { useSession } from 'next-auth/react';
import React from 'react';
import { EnhancedProfileHeader } from './EnhancedProfileHeader';
import MyMediaGallery from './MyMediaGallery';
import { UserInteractionsSection } from './UserInteractionsSection';

const ProfilePageComponent: React.FC = () => {
	const { data: session } = useSession();

	// Handler for profile updates from enhanced components
	const handleProfileUpdate = (updatedProfile: Partial<UserProfile>) => {
		// Profile updates will be handled by the individual components
		console.log('Profile updated:', updatedProfile);
	};

	return (
		<div
			className='min-h-screen'
			style={{
				background: `linear-gradient(135deg,
					rgba(14, 165, 233, 0.04) 0%,
					rgba(34, 197, 94, 0.03) 25%,
					rgba(56, 189, 248, 0.02) 50%,
					rgba(74, 222, 128, 0.03) 75%,
					rgba(20, 184, 166, 0.02) 100%
				), #FFFFFF`,
			}}>
			{/* Responsive Container - Following Design Rules */}
			<div className='w-full px-4 md:px-6 lg:px-12 max-w-7xl mx-auto py-8 md:py-12 lg:py-16'>
				{/* Shared User Interactions Provider */}
				{session?.user?.id && (
					<UserInteractionsProvider userId={session.user.id}>
						{/* Profile Header Card - Square Edges */}
						<div
							className='border backdrop-blur-sm mb-4 md:mb-6 lg:mb-8 overflow-hidden'
							style={{
								background: 'rgba(255, 255, 255, 0.9)',
								borderColor: colors.ui.gray200,
							}}>
							{/* Header Cover Section */}
							<div
								className='relative h-32 md:h-40 lg:h-48 overflow-hidden'
								style={{
									background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
								}}>
								{/* Pattern Overlay */}
								<div className='absolute inset-0 opacity-10'>
									<div
										className='absolute inset-0'
										style={{
											backgroundImage: `radial-gradient(circle at 25% 25%, rgba(255,255,255,0.4) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(255,255,255,0.3) 0%, transparent 50%)`,
											backgroundSize: '80px 80px, 120px 120px',
										}}></div>
								</div>

								{/* Floating Elements - Square Shapes */}
								<div className='absolute top-4 md:top-6 right-4 md:right-6 w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm'></div>
								<div className='absolute bottom-6 md:bottom-8 left-6 md:left-8 w-6 h-6 md:w-8 md:h-8 bg-white/20 backdrop-blur-sm'></div>
							</div>

							{/* Main Profile Content */}
							<div className='relative p-4 md:p-6 lg:p-8'>
								{/* Profile Header Section */}
								<div className='flex flex-col gap-4 md:gap-6 -mt-12 md:-mt-16 lg:-mt-20 mb-4 md:mb-6 lg:mb-8'>
									<EnhancedProfileHeader
										user={session?.user}
										onProfileUpdate={handleProfileUpdate}
										integrated={true}
									/>
								</div>

								{/* Interactions Section - Square Edges */}
								<div
									className='border backdrop-blur-sm p-4 md:p-6 lg:p-8'
									style={{
										background: 'rgba(255, 255, 255, 0.9)',
										borderColor: colors.ui.gray200,
									}}>
									<UserInteractionsSection
										userId={session.user.id}
										integrated={true}
									/>
								</div>
							</div>
						</div>

						{/* Media Gallery Section - Full Width */}
						<div className='mt-4 md:mt-6 lg:mt-8'>
							<MyMediaGallery />
						</div>
					</UserInteractionsProvider>
				)}

				{/* Fallback for unauthenticated users */}
				{!session?.user?.id && (
					<div className='text-center py-8 md:py-12 lg:py-16'>
						<div
							className='border backdrop-blur-sm p-4 md:p-6 lg:p-8 max-w-md mx-auto'
							style={{
								background: 'rgba(255, 255, 255, 0.9)',
								borderColor: colors.ui.gray200,
							}}>
							<h2
								className='text-xl md:text-2xl lg:text-3xl font-bold mb-4'
								style={{ color: colors.neutral.textBlack }}>
								Please sign in to view your profile
							</h2>
							<p
								className='text-sm md:text-base'
								style={{ color: colors.neutral.slateGray }}>
								You need to be authenticated to access your profile page.
							</p>
						</div>
					</div>
				)}
			</div>
		</div>
	);
};

export default ProfilePageComponent;
